const { config } = require('aws-sdk');
const { NudgeRule } = require('../src/nudgeEngine');
const daysToLive = config.nudgeTTLDays || 2;
class SleepMealTimingRule extends NudgeRule {
  constructor(params = {}) {
    super("sleep_meal_gap", "Encourage early dinner before sleep.");
    this.minHours = params.minHours || 4;
  }

  applies({ data }) {
    const lastMealTime = data.mealLogs?.date || null;
    const sleepTime = data.sleepLogs?.startTime || null;
    if (!lastMealTime || !sleepTime) return false;
    const gap = (new Date(sleepTime) - new Date(lastMealTime)) / (1000 * 60 * 60);
    return (gap > 0) && (gap < this.minHours);
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "steps";
    const title = "Too Close to Bedtime?";
    const message = `Try to finish your last meal at least ${this.minHours} hours before bedtime.`;
    return getNudgeDocument(userId, category, title, message);
  }
}

class StepTargetShortfallRule extends NudgeRule {
  constructor(params = {}) {
    super("step_target_shortfall", "Nudge user if behind on weekly step goal.");
    this.thresholdPercent = params.thresholdPercent || 0.8;
  }

  applies({ data }) {
    const { stepsThisWeek = 0, stepGoalPerDay = 0, daysLogged = 0 } = data || {};
    const expected = stepGoalPerDay * daysLogged;
    return stepsThisWeek < expected * this.thresholdPercent;
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "steps";
    const title = "Step Up Your Week!";
    const message = "You're a little behind on your step goal. A short walk today helps!";
    return getNudgeDocument(userId, category, title, message);
  }
}

class InactivityNudgeRule extends NudgeRule {
  constructor(params = {}) {
    super("user_inactivity", "Encourage return after period of inactivity.");
    this.inactiveDays = params.inactiveDays || 7;
  }

  applies({ data }) {
    const lastActiveDate = data.activityLogs.timestamp || null;
    if (!lastActiveDate) return false;
    const daysSince = (Date.now() - new Date(lastActiveDate)) / (1000 * 60 * 60 * 24);
    return daysSince >= this.inactiveDays;
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "activity";
    const title = "We Miss You!";
    const message = "We haven’t heard from you in a while. Ready to restart with a small step today?";
    return getNudgeDocument(userId, category, title, message);
  }
}

function getNudgeDocument(userId, category, title, message) {
  const currentTime = new Date();
  const ttlDate = new Date(currentTime.getTime() + daysToLive * 24 * 60 * 60 * 1000);

  const doc = {
    userId,
    type: "nudge",
    content: {
      title,
      message,
    },
    timestamp: currentTime.toISOString(),
    category,
    priority: "medium",
    ttl: ttlDate.toISOString(),
    status: "open"
  };

  return doc;
}

module.exports = {
  SleepMealTimingRule,
  StepTargetShortfallRule,
  InactivityNudgeRule
};